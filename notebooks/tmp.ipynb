import pandas as pd
import os
import re

frieren_files = os.listdir("/cm/shared/sonnn45/Caption-generator/output/Frieren-V2A-results/_CFG4.5_CG50_euler_26_useDG_Falsegen_wav_16k_80")
len(frieren_files)

frieren_ids = ["_".join(file.split("_")[:-2]) for file in frieren_files]
frieren_ids = list(set(frieren_ids))
len(frieren_ids)

frieren_files

frieren_ids

test_files = pd.read_csv("/cm/shared/thanhtvt/Flowley/dataset/VGGSound/memmap_fps8-dim768/train.tsv", sep="\t")
# test_files = test_files[test_files["split"] == "test"]
# test_files = test_files[test_files["path"].str.contains("frieren")]
test_files = test_files["path"].tolist()
test_files

test_ids = [os.path.basename(file).split(".")[0] for file in test_files]
test_ids = ["_".join(id.split("_")[:-1]) for id in test_ids]
len(test_ids)

# Check if all test_ids are in frieren_ids
counter = 0
not_appeared = []
for id in test_ids:
    if id not in frieren_ids:
        counter += 1
        not_appeared.append(id)
print(counter)
not_appeared

counter