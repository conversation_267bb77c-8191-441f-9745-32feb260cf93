{"cells": [{"cell_type": "code", "execution_count": 5, "id": "38e60713", "metadata": {}, "outputs": [], "source": ["import torch\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns"]}, {"cell_type": "code", "execution_count": 2, "id": "80f506d8", "metadata": {}, "outputs": [], "source": ["from flowley.model.flow_matching import logit_normal_sample, cosmap"]}, {"cell_type": "code", "execution_count": 13, "id": "3c820131", "metadata": {}, "outputs": [], "source": ["bs=10000\n", "\n", "def x2(bs):\n", "    x = torch.randn(bs)\n", "    return torch.sigmoid(x)**2\n", "\n", "lognorm_arr = logit_normal_sample(bs=bs)\n", "cosmap_arr = cosmap(bs=bs)\n", "x2_arr = x2(bs=bs)"]}, {"cell_type": "code", "execution_count": 6, "id": "c11ed093", "metadata": {}, "outputs": [{"data": {"text/plain": ["<seaborn.axisgrid.FacetGrid at 0x15534d672590>"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 500x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.displot(lognorm_arr)"]}, {"cell_type": "code", "execution_count": 14, "id": "5f80a7d8", "metadata": {}, "outputs": [{"data": {"text/plain": ["<seaborn.axisgrid.FacetGrid at 0x15534d0762f0>"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 500x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.displot(x2_arr)"]}, {"cell_type": "code", "execution_count": 8, "id": "4d8c7d69", "metadata": {}, "outputs": [{"data": {"text/plain": ["<seaborn.axisgrid.FacetGrid at 0x155338812f80>"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 500x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.displot(cosmap_arr)"]}], "metadata": {"kernelspec": {"display_name": "flowley", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 5}