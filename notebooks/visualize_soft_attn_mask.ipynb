{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/miniconda3/envs/flowley/lib/python3.10/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}], "source": ["import torch\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "from flowley.model.modules.layers.attention import compute_audio_visual_cross_attn_mask"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["audio_len = 250\n", "video_len = 32\n", "audio_fps = 31.25\n", "video_fps = 4\n", "window_size = 0\n", "fade = False\n", "fade_range = 0\n"]}, {"cell_type": "code", "execution_count": 57, "metadata": {}, "outputs": [{"data": {"text/plain": ["torch.<PERSON><PERSON>([125, 32])"]}, "execution_count": 57, "metadata": {}, "output_type": "execute_result"}], "source": ["audio_len = 125\n", "video_len = 32\n", "audio_fps = 31.25\n", "video_fps = 8\n", "window_size = 0\n", "fade = True\n", "fade_range = 4\n", "fade_type = \"cosine\"\n", "\n", "mask = compute_audio_visual_cross_attn_mask(\n", "    audio_len=audio_len,\n", "    video_len=video_len,\n", "    video_fps=video_fps,\n", "    audio_fps=audio_fps,\n", "    window_size=window_size,\n", "    fade=fade,\n", "    fade_type=fade_type,\n", "    fade_range=fade_range,\n", "    fade_scale=0.0\n", ")\n", "mask.shape"]}, {"cell_type": "code", "execution_count": 58, "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([-1.3816e+01, -1.3816e+01, -1.3816e+01, -1.3816e+01, -1.3816e+01,\n", "        -1.3816e+01, -1.3816e+01, -1.3816e+01, -1.3816e+01, -1.3816e+01,\n", "        -1.3816e+01, -1.3816e+01, -1.3816e+01, -1.3816e+01, -1.3816e+01,\n", "         9.5367e-07, -1.3816e+01, -1.3816e+01, -1.3816e+01, -1.3816e+01,\n", "        -1.3816e+01, -1.3816e+01, -1.3816e+01, -1.3816e+01, -1.3816e+01,\n", "        -1.3816e+01, -1.3816e+01, -1.3816e+01, -1.3816e+01, -1.3816e+01,\n", "        -1.3816e+01, -1.3816e+01])"]}, "execution_count": 58, "metadata": {}, "output_type": "execute_result"}], "source": ["mask[60]"]}, {"cell_type": "code", "execution_count": 59, "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[ 9.5367e-07, -1.3816e+01, -1.3816e+01,  ..., -1.3816e+01,\n", "         -1.3816e+01, -1.3816e+01],\n", "        [ 9.5367e-07, -1.3816e+01, -1.3816e+01,  ..., -1.3816e+01,\n", "         -1.3816e+01, -1.3816e+01],\n", "        [-1.3816e+01,  9.5367e-07, -1.3816e+01,  ..., -1.3816e+01,\n", "         -1.3816e+01, -1.3816e+01],\n", "        ...,\n", "        [-1.3816e+01, -1.3816e+01, -1.3816e+01,  ..., -1.3816e+01,\n", "         -1.3816e+01,  9.5367e-07],\n", "        [-1.3816e+01, -1.3816e+01, -1.3816e+01,  ..., -1.3816e+01,\n", "         -1.3816e+01,  9.5367e-07],\n", "        [-1.3816e+01, -1.3816e+01, -1.3816e+01,  ..., -1.3816e+01,\n", "         -1.3816e+01,  9.5367e-07]])"]}, "execution_count": 59, "metadata": {}, "output_type": "execute_result"}], "source": ["mask"]}, {"cell_type": "code", "execution_count": 60, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1100x400 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.figure(figsize=(11, 4))\n", "plt.imshow(mask.T, cmap=\"gray\", aspect=\"auto\")\n", "plt.xlabel(\"Audio frames\")\n", "plt.ylabel(\"Video frames\")\n", "plt.title(f\"Local cross-attention mask (transpose) with window_size={window_size} and fade_range={fade_range}\")\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["audio_len = 250\n", "video_len = 32\n", "audio_fps = 31.25\n", "video_fps = 4\n", "window_size = 1"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["mask = time_based_local_cross_attention_mask(\n", "    audio_len, video_len, audio_fps, video_fps, window_size\n", ").numpy()"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.figure(figsize=(10, 5))\n", "plt.imshow(mask, cmap=\"gray\", aspect=\"auto\")\n", "plt.xlabel(\"Video frames\")\n", "plt.ylabel(\"Audio frames\")\n", "plt.title(\"Local cross-attention mask\")\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["def time_based_fade_cross_attention_mask(audio_len, video_len, audio_fps, video_fps, window_size):\n", "    \"\"\"\n", "    Creates a soft (fade-in/fade-out) mask of shape [audio_len, video_len] for cross-attention.\n", "    Each audio token at index i is aligned to a center video frame:\n", "        t_i = i / audio_fps\n", "        j(i) = round(t_i * video_fps)\n", "    Then, for each audio index i and video frame j, if |j - j(i)| <= window_size,\n", "    assign a weight that linearly decays from 1 (at the center) to 0 (at the window edge).\n", "    Frames beyond the window receive a weight of 0.\n", "    \"\"\"\n", "    # Compute time for each audio token and determine its aligned video frame (using round)\n", "    i = torch.arange(audio_len)            # [audio_len]\n", "    t_i = i.float() / audio_fps            # time for each audio token in seconds\n", "    j_center = torch.round(t_i * video_fps).long()  # aligned video frame indices\n", "    j_center = torch.clamp(j_center, 0, video_len - 1)\n", "    \n", "    # Create a grid of video indices for each audio token\n", "    j = torch.arange(video_len).unsqueeze(0).expand(audio_len, video_len)  # [audio_len, video_len]\n", "    j_center = j_center.unsqueeze(1).expand(audio_len, video_len)          # [audio_len, video_len]\n", "    \n", "    # Compute the absolute distance from the center for each pair (i, j)\n", "    dist = (j.float() - j_center.float()).abs()\n", "    \n", "    # Compute weights: for distances within the window, weight decays linearly from 1 to 0.\n", "    # For distances beyond the window, weight is 0.\n", "    weights = torch.where(dist <= window_size, 1 - (dist / window_size), torch.zeros_like(dist))\n", "    return weights"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[1.  , 0.75, 0.5 , ..., 0.  , 0.  , 0.  ],\n", "       [1.  , 0.75, 0.5 , ..., 0.  , 0.  , 0.  ],\n", "       [1.  , 0.75, 0.5 , ..., 0.  , 0.  , 0.  ],\n", "       ...,\n", "       [0.  , 0.  , 0.  , ..., 0.5 , 0.75, 1.  ],\n", "       [0.  , 0.  , 0.  , ..., 0.5 , 0.75, 1.  ],\n", "       [0.  , 0.  , 0.  , ..., 0.5 , 0.75, 1.  ]], dtype=float32)"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["mask = time_based_fade_cross_attention_mask(\n", "    audio_len, video_len, audio_fps, video_fps, window_size\n", ").numpy()\n", "\n", "mask"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"image/png": "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**********************************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", "text/plain": ["<Figure size 1000x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.figure(figsize=(10, 5))\n", "plt.imshow(mask, cmap=\"gray\", aspect=\"auto\")\n", "plt.xlabel(\"Video frames\")\n", "plt.ylabel(\"Audio frames\")\n", "plt.title(\"Local cross-attention mask\")\n", "plt.show()"]}], "metadata": {"kernelspec": {"display_name": "flowley", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 2}