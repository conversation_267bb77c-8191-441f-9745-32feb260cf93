{"cells": [{"cell_type": "code", "execution_count": 1, "id": "c3da071d", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import os\n", "import torch\n", "import numpy as np\n", "from tensordict import TensorDict"]}, {"cell_type": "code", "execution_count": 15, "id": "f760a0d7", "metadata": {}, "outputs": [], "source": ["root_dir = '/cm/shared/thanhtvt/Flowley/dataset/VGGSound/memmap_fps8-dim768'\n", "\n", "tsv_path = os.path.join(root_dir, \"train.tsv\")\n", "train_df = pd.read_csv(tsv_path, sep=\"\\t\")\n", "train_td = TensorDict.load_memmap(os.path.join(root_dir, \"train\"))\n", "# check len of train_df and train_td\n", "assert len(train_df) == len(train_td[\"mean\"])"]}, {"cell_type": "code", "execution_count": 16, "id": "8f9ba095", "metadata": {}, "outputs": [], "source": ["# Similar to valid\n", "tsv_path = os.path.join(root_dir, \"valid.tsv\")\n", "valid_df = pd.read_csv(tsv_path, sep=\"\\t\")\n", "valid_td = TensorDict.load_memmap(os.path.join(root_dir, \"valid\"))\n", "assert len(valid_df) == len(valid_td[\"mean\"])"]}, {"cell_type": "code", "execution_count": 17, "id": "91ff0a1b", "metadata": {}, "outputs": [], "source": ["# Randomly select n rows from valid_df\n", "n = 10000\n", "selected_df = valid_df.sample(n=n, random_state=42)\n", "# Get index of selected rows\n", "selected_ids = selected_df.index.tolist()\n", "# Get corresponding rows from valid_td\n", "selected_td = valid_td[selected_ids]\n", "# Check length\n", "assert len(selected_df) == len(selected_td[\"mean\"])\n", "# Concat these values to train\n", "new_train_df = pd.concat([train_df, selected_df], axis=0, ignore_index=True)\n", "new_train_td = TensorDict(\n", "    {\n", "        \"mean\": torch.concat([train_td[\"mean\"], selected_td[\"mean\"]], dim=0),\n", "        \"std\": torch.concat([train_td[\"std\"], selected_td[\"std\"]], dim=0),\n", "        \"video_features\": torch.concat([train_td[\"video_features\"], selected_td[\"video_features\"]], dim=0),\n", "        \"text_features\": torch.concat([train_td[\"text_features\"], selected_td[\"text_features\"]], dim=0),\n", "        \"text_len\": torch.concat([train_td[\"text_len\"], selected_td[\"text_len\"]], dim=0),\n", "    },\n", "    batch_size=len(new_train_df)\n", ")\n", "# Check length\n", "assert len(new_train_df) == len(new_train_td[\"mean\"])"]}, {"cell_type": "code", "execution_count": 27, "id": "6c4995ed", "metadata": {}, "outputs": [], "source": ["# Remove selected_ids from valid_df and valid_td\n", "new_valid_df = valid_df.drop(selected_ids, axis=0)\n", "mask = torch.ones(len(valid_td[\"mean\"]), dtype=torch.bool)\n", "mask[selected_ids] = False\n", "new_valid_td = valid_td[mask]\n", "assert len(new_valid_df) == len(new_valid_td[\"mean\"])"]}, {"cell_type": "code", "execution_count": 29, "id": "c4dd6751", "metadata": {}, "outputs": [{"data": {"text/plain": ["5706"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["len(new_valid_td)"]}, {"cell_type": "code", "execution_count": 31, "id": "839df24c", "metadata": {}, "outputs": [{"data": {"text/plain": ["TensorDict(\n", "    fields={\n", "        mean: MemoryMappedTensor(shape=torch.Size([5706, 250, 20]), device=cpu, dtype=torch.float32, is_shared=True),\n", "        std: MemoryMappedTensor(shape=torch.Size([5706, 250, 20]), device=cpu, dtype=torch.float32, is_shared=True),\n", "        text_features: MemoryMappedTensor(shape=torch.Size([5706, 77, 1024]), device=cpu, dtype=torch.float32, is_shared=True),\n", "        text_len: MemoryMappedTensor(shape=torch.Size([5706]), device=cpu, dtype=torch.int64, is_shared=True),\n", "        video_features: MemoryMappedTensor(shape=torch.Size([5706, 64, 768]), device=cpu, dtype=torch.float32, is_shared=True)},\n", "    batch_size=torch.Size([5706]),\n", "    device=cpu,\n", "    is_shared=False)"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["# Save new_train_df and new_train_td\n", "saved_dir = \"/cm/shared/thanhtvt/Flowley/dataset/VGGSound/memmap_fps8-dim768_v2\"\n", "new_train_df.to_csv(os.path.join(saved_dir, \"train.tsv\"), sep=\"\\t\", index=False)\n", "new_train_td.memmap_(os.path.join(saved_dir, \"train\"), copy_existing=True)\n", "\n", "# Save new_valid_df and new_valid_td\n", "new_valid_df.to_csv(os.path.join(saved_dir, \"valid.tsv\"), sep=\"\\t\", index=False)\n", "new_valid_td.memmap_(os.path.join(saved_dir, \"valid\"), copy_existing=True)"]}, {"cell_type": "code", "execution_count": 32, "id": "a151dc2e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Train: Before 166037, After 176037\n", "Valid: Before 15706, After 5706\n"]}], "source": ["print(f\"Train: Before {len(train_df)}, After {len(new_train_df)}\")\n", "print(f\"Valid: Before {len(valid_df)}, After {len(new_valid_df)}\")"]}], "metadata": {"kernelspec": {"display_name": "flowley", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 5}