defaults:
  - train
  - _self_

run_dir: null  # have to specify this in the command line

train_cfg_path: ${run_dir}/train_cfg.yaml

hydra:
  job_logging:
    handlers:
      console:
        class: logging.StreamHandler
        formatter: simple
        stream: ext://sys.stdout
      file:
        class: logging.FileHandler
        formatter: simple
        # absolute file path
        filename: ${run_dir}/${hydra.job.name}.log

data:
  test_dataset:
    tsv_file: ${paths.data_dir}/VGGSound/memmap_fps8-dim768/test.tsv
    memmap_dir: ${paths.data_dir}/VGGSound/memmap_fps8-dim768/test

# EMA
min_step_ema: null
max_step_ema: null

# CFG
cfg_strength: 4.5

vocode_gt: True
force_synthesize: False

batch_size: 256
num_workers: 32

duration: 8.0

num_test_log_data: 2

export_with_video: False

video_root_dir: ${paths.data_dir}/VGGSound/scratch/shared/beegfs/hchen/train_data/VGGSound_final/video