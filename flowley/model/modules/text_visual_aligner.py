import torch.nn as nn
from torch import Tensor
from einops import rearrange
from .layers.low_levels import ConvMLP
from .layers.attention import TransformerLayer
from .layers.positional_embeddings import SinusoidalEmbedding


class TextVisualAligner(nn.Module):

    def __init__(
        self,
        hidden_dim: int,
        ff_multiplier: int,
        n_heads: int = 4,
        n_layers: int = 2,
        pooling: str = "cls",
        attn_dropout: float = 0.0,
        mlp_dropout: float = 0.0,
        qk_norm: bool = True,
        qk_scale: float = None,
        rms: bool = True,
    ):
        super(TextVisualAligner, self).__init__()
        self.textual_spatial_aware = TextualSpatialAwareModule(
            dim=hidden_dim,
            ff_multiplier=ff_multiplier,
            n_heads=n_heads,
            n_layers=n_layers,
            qk_norm=qk_norm,
            qk_scale=qk_scale,
            rms=rms,
            attn_dropout=attn_dropout,
            mlp_dropout=mlp_dropout,
            pooling=pooling,
        )
        self.out_prj = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim),
            nn.SiLU(),
            ConvMLP(hidden_dim, hidden_dim * 2, mlp_dropout, kernel_size=3, padding=1),
        )

    def forward(self, text_feat: Tensor, video_feat: Tensor) -> Tensor:
        """
        Args:
            text_feat (Tensor): Text feature tensor with shape of (batch_size, seq_len, text_dim).
            video_feat (Tensor): Video feature tensor with shape of
                                       (batch_size, frames, grid_size**2 + 1, video_dim).

        Returns:
            Tensor: Aligned video feature tensor with shape of (batch_size, frames, video_dim).
        """
        aligned_video_feat = self.textual_spatial_aware(text_feat, video_feat)
        return self.out_prj(aligned_video_feat)


class TextualVideoAwareModule(nn.Module):

    def __init__(
        self,
        dim: int,
        ff_multiplier: int,
        n_heads: int = 4,
        n_layers: int = 2,
        qk_norm: bool = True,
        qk_scale: float = None,
        rms: bool = True,
        attn_dropout: float = 0.0,
        mlp_dropout: float = 0.0,
        pe_dropout: float = 0.0,
        max_len: int = 10000,
    ):
        super(TextualVideoAwareModule, self).__init__()
        layer = TransformerLayer(
            hidden_dim=dim,
            ff_multiplier=ff_multiplier,
            n_heads=n_heads,
            qk_norm=qk_norm,
            qk_scale=qk_scale,
            rms=rms,
            attn_dropout=attn_dropout,
            attn_type="cross",
            mlp_dropout=mlp_dropout,
        )
        self.layers = nn.ModuleList([layer for _ in range(n_layers)])
        self.pe_seq = SinusoidalEmbedding(dim, pe_dropout, max_len=max_len, mode="seq")

    def forward(self, text_feat: Tensor, video_feat: Tensor) -> Tensor:
        raise NotImplementedError


class TextualSpatialAwareModule(TextualVideoAwareModule):

    def __init__(
        self,
        dim: int,
        ff_multiplier: int,
        n_heads: int = 4,
        n_layers: int = 2,
        qk_norm: bool = True,
        qk_scale: float = None,
        rms: bool = True,
        attn_dropout: float = 0.0,
        mlp_dropout: float = 0.0,
        pe_dropout: float = 0.0,
        max_len: int = 10000,
        grid: int = 27,
        pooling: str = "cls",
    ):
        super(TextualSpatialAwareModule, self).__init__(
            dim=dim,
            ff_multiplier=ff_multiplier,
            n_heads=n_heads,
            n_layers=n_layers,
            qk_norm=qk_norm,
            qk_scale=qk_scale,
            rms=rms,
            attn_dropout=attn_dropout,
            mlp_dropout=mlp_dropout,
            pe_dropout=pe_dropout,
            max_len=max_len,
        )
        self.pooling = pooling
        self.pe_visual = SinusoidalEmbedding(dim, pe_dropout, grid=grid, mode="vit")

    def global_pool(self, x: Tensor) -> Tensor:
        if self.pooling == "cls":
            return x[:, :, 0]
        elif self.pooling == "gated":
            pass
        elif self.pooling == "average":
            pass
        elif self.pooling == "attention":
            pass
        else:
            raise ValueError(f"Invalid pooling type: {self.pooling}")

    def forward(self, text_feat: Tensor, video_feat: Tensor) -> Tensor:
        """
        Args:
            text_feat (Tensor): Text feature tensor with shape of (batch_size, seq_len, dim).
            video_feat (Tensor): Video feature tensor with shape of
                                       (batch_size, frames, grid_size**2 + 1, dim).

        Returns:
            Tensor: Aligned video feature tensor with shape of (batch_size, frames, dim).
        """
        n_frames = video_feat.size(1)
        video_states = rearrange(video_feat, 'b f p d -> (b f) p d')
        video_states = self.pe_visual(video_states)

        # duplicate text_feat for each frame
        text_feat = text_feat.unsqueeze(1).expand(-1, n_frames, -1, -1)
        text_feat = rearrange(text_feat, 'b f l d -> (b f) l d')
        text_feat = self.pe_seq(text_feat)

        for layer in self.layers:
            video_states = layer(video_states, text_feat, rot=None)
        video_states = rearrange(video_states, '(b f) p d -> b f p d', f=n_frames)

        return self.global_pool(video_states)
