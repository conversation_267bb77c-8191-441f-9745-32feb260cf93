import torch
import torch.nn as nn
import math
from functools import partial
from typing import List
from torchdiffeq import odeint
from scipy.optimize import linear_sum_assignment
from ema_pytorch import EMA
from .model import Flowley
from .losses import (
    PseudoHuberLoss,
    VelocityDirectionLoss,
    InfoNCELoss,
    LossBreakdown,
    make_segments
)
from accelerate.logging import get_logger


logger = get_logger(__name__)


def cosmap(bs: int) -> torch.Tensor:
    # Equation 21 in SD3 paper https://arxiv.org/abs/2403.03206
    u = torch.rand(bs)
    return 1 - (1 / (torch.tan(math.pi / 2 * u) + 1))


def logit_normal_sample(bs: int, m: float = 0., s: float = 1.) -> torch.Tensor:
    # Logit-normal sampling introduced in SD3 paper
    s = torch.randn(bs) * s + m
    return torch.sigmoid(s)


# Adapted from https://github.com/lucidrains/rectified-flow-pytorch
# and https://github.com/hkchengrex/MMAudio
class RectifiedFlowMatching(nn.Module):

    def __init__(
        self,
        model: Flowley,
        loss_type: str = "mse",
        ode_solver: str = "euler",
        noise_scheduler: str = "cosmap",
        lns_mean: float = 0.0,
        lns_scale: float = 0.1,
        num_steps: int = 25,
        ema_update_after_step: int = 100,
        use_consistency: bool = False,
        consistency_decay: float = 0.9999,
        consistency_velocity_match_alpha: float = 1e-5,
        consistency_delta_time: float = 1e-3,
        consistency_loss_weight: float = 1.0,
        anchor_loss_weight: float = 1.0,
        direction_loss_weight: float = 1.0,
        contrastive_mode: List[str] = ['segment'],
        contrastive_loss_weight: float = 0.0,
        contrastive_temperature: float = 0.07,
        contrastive_vid_segment_sz: int = 4,
        contrastive_stride_segment: int = 2,
        contrastive_n_segments: int = None,
        contrastive_random_start: bool = False,
        contrastive_segment_split: bool = False,
        immiscible: bool = False,
        **kwargs
    ):

        super(RectifiedFlowMatching, self).__init__()

        self.model = model
        if loss_type == "mse":
            self.loss_fn = nn.MSELoss()
        elif loss_type == "pseudo_huber":
            self.loss_fn = PseudoHuberLoss()
        else:
            raise ValueError(f"Unknown loss type: {loss_type}")
        self.direction_loss_fn = VelocityDirectionLoss()
        self.anchor_loss_fn = nn.MSELoss()
        self.contrastive_loss_fn = InfoNCELoss(contrastive_temperature)
        if "segment" in contrastive_mode:
            self.aud_segment_proj = nn.Linear(model.hidden_dim, model.hidden_dim)
            self.vid_segment_proj = nn.Linear(model.hidden_dim, model.hidden_dim)
            self.segment_infonce = InfoNCELoss(contrastive_temperature)
        elif "global" in contrastive_mode:
            self.aud_global_proj = nn.Linear(model.hidden_dim, model.hidden_dim)
            self.vid_global_proj = nn.Linear(model.hidden_dim, model.hidden_dim)
            self.global_infonce = InfoNCELoss(contrastive_temperature)
        else:
            raise ValueError(f"Unsupported contrastive mode: {contrastive_mode}")

        if noise_scheduler == "cosmap":
            self.noise_scheduler = partial(cosmap)
        elif noise_scheduler == "logit_normal":
            self.noise_scheduler = partial(
                logit_normal_sample, m=lns_mean, s=lns_scale
            )
        elif not noise_scheduler:
            self.noise_scheduler = nn.Identity()
        else:
            raise ValueError(f"Unknown noise scheduler: {noise_scheduler}")

        self.ode_solver = ode_solver
        self.num_steps = num_steps
        # Consistency Flow Matching
        self.use_consistency = use_consistency
        self.consistency_velocity_match_alpha = consistency_velocity_match_alpha
        self.consistency_delta_time = consistency_delta_time
        self.consistency_loss_weight = consistency_loss_weight
        # Auxilliary losses
        self.anchor_loss_weight = anchor_loss_weight
        self.direction_loss_weight = direction_loss_weight
        self.contrastive_loss_weight = contrastive_loss_weight
        self.contrastive_mode = contrastive_mode
        self.contrastive_video_segment_sz = contrastive_vid_segment_sz
        self.contrastive_stride_segment = contrastive_stride_segment
        self.contrastive_n_segments = contrastive_n_segments
        self.random_start = contrastive_random_start
        self.segment_split = contrastive_segment_split

        if use_consistency:
            self.ema_model = EMA(
                model,
                beta=consistency_decay,
                update_after_step=ema_update_after_step,
                include_online_model=False
            )
            logger.info("Use consistency. EMA model initialized!")

        self.immiscible = immiscible

    @property
    def device(self):
        return next(self.model.parameters()).device

    def predict_flow(
        self,
        model: nn.Module,
        latent: torch.Tensor,
        text_feat: torch.Tensor,
        visual_feat: torch.Tensor,
        text_len: torch.Tensor,
        t: torch.Tensor
    ):
        if t.ndim == 3:
            t = t.squeeze(dim=(1, 2))
        elif t.ndim == 0:
            t = t.unsqueeze(0)
        assert t.ndim == 1, f"t should have the ndim=1 but {t.ndim}"
        flow, proj_visual_feat = model(latent, text_feat, visual_feat, text_len, t=t)
        return flow, proj_visual_feat

    def inference(
        self,
        visual_feat: torch.Tensor,
        text_feat: torch.Tensor,
        text_len: torch.Tensor,
        cfg_strength: float,
        t: torch.Tensor,
        latent: torch.Tensor,
    ):
        bs = latent.size(0)
        empty_visual_feat, empty_text_feat = self.model.get_empty_conditions(bs)

        if cfg_strength < 1.0:
            return self.predict_flow(self.model, latent, text_feat, visual_feat, text_len, t)[0]
        else:
            return (
                cfg_strength *
                self.predict_flow(self.model, latent, text_feat, visual_feat, text_len, t)[0] +
                (1 - cfg_strength) *
                self.predict_flow(
                    self.model, latent, empty_text_feat, empty_visual_feat, text_len, t)[0]
            )

    def get_noises_and_flows(
        self,
        model: nn.Module,
        latent: torch.Tensor,
        visual_feat: torch.Tensor,
        text_feat: torch.Tensor,
        text_len: torch.Tensor,
        noise: torch.Tensor,
        t: torch.Tensor
    ):

        # Get intermediate latent
        xt = t * latent + (1. - t) * noise

        # Flows
        tgt_flow = latent - noise
        pred_flow, proj_visual_feat = self.predict_flow(
            model, xt, text_feat, visual_feat, text_len, t
        )

        # Predicted latent
        pred_latent = xt + (1. - t) * pred_flow

        return xt, pred_latent, pred_flow, tgt_flow, proj_visual_feat

    @torch.no_grad()
    def sample(
        self,
        text_feat: torch.Tensor,
        visual_feat: torch.Tensor,
        text_len: torch.Tensor,
        data_shape: list[int],
        batch_size: int = 1,
        noise: torch.Tensor = None,
        use_ema: bool = False,
        inference: bool = False,
        cfg_strength: float = None,
    ):
        model = self.ema_model if use_ema else self.model
        was_training = self.training
        self.eval()

        def ode_fn(t, x):
            flow, _ = self.predict_flow(model, x, text_feat, visual_feat, text_len, t)
            return flow

        if inference:
            assert cfg_strength is not None, "cfg_strength should be provided for inference"
            fn = partial(self.inference, visual_feat, text_feat, text_len, cfg_strength)
        else:
            fn = ode_fn

        noise = torch.randn(batch_size, *data_shape, device=self.device) if noise is None else noise

        t = torch.linspace(0., 1., self.num_steps, device=self.device)

        if self.ode_solver == "euler":
            sampled_latent = noise
            steps = torch.linspace(0., 1., self.num_steps + 1, device=self.device)
            for ti, t in enumerate(steps[:-1]):
                flow = fn(t, sampled_latent)
                next_t = steps[ti + 1]
                dt = next_t - t
                sampled_latent = sampled_latent + dt * flow
        else:
            trajectory = odeint(fn, noise, t, method=self.ode_solver)
            sampled_latent = trajectory[-1]

        self.train(was_training)

        return self.model.denormalize(sampled_latent)

    def forward(
        self,
        latent: torch.Tensor,
        visual_feat: torch.Tensor,
        text_feat: torch.Tensor,
        text_len: torch.Tensor,
        noise: torch.Tensor = None,
        return_contrastive: bool = False,
    ):

        noise = torch.randn_like(latent) if noise is None else noise
        latent = self.model.normalize(latent)

        if self.immiscible:
            cost = torch.cdist(latent.flatten(1), noise.flatten(1))
            _, reorder_indices = linear_sum_assignment(cost.cpu())
            noise = noise[torch.from_numpy(reorder_indices).to(cost.device)]

        # Get timestep
        t = self.noise_scheduler(bs=latent.size(0)).to(latent.device)
        t = t[:, None, None]
        if self.use_consistency:
            t *= 1. - self.consistency_delta_time

        # Get noises and flows
        _, pred_latent, pred_flow, tgt_flow, proj_visual_feat = self.get_noises_and_flows(
            self.model, latent, visual_feat, text_feat, text_len, noise, t
        )
        if self.use_consistency:
            delta_t = self.consistency_delta_time
            _, ema_pred_latent, ema_pred_flow, _, _ = self.get_noises_and_flows(
                self.ema_model, latent, visual_feat, text_feat, text_len, noise, t + delta_t
            )

        # Compute losses
        main_loss = self.loss_fn(pred_flow, tgt_flow)
        direction_loss = self.direction_loss_fn(pred_flow, tgt_flow)    # FasterDiT
        anchor_loss = self.anchor_loss_fn(pred_latent, latent)

        proj_video_segments, proj_audio_segments = None, None
        proj_vid_feat, proj_aud_feat = None, None
        if self.contrastive_loss_weight != 0.0:
            segment_loss = 0.
            global_loss = 0.
            latent_hat = self.model.audio_projector(pred_latent)
            if "segment" in self.contrastive_mode:
                video_segments, audio_segments = make_segments(
                    proj_visual_feat,
                    latent_hat,
                    video_segment_sz=self.contrastive_video_segment_sz,
                    segment_stride=self.contrastive_stride_segment,
                    n_segments=self.contrastive_n_segments,
                    video_fps=self.model.video_fps,
                    audio_fps=self.model.audio_fps,
                    random_start=self.random_start
                )
                # Average across time dim and flatten them out for contrastive calculation
                proj_video_segments = video_segments.mean(2).view(-1, self.model.hidden_dim)
                proj_audio_segments = audio_segments.mean(2).view(-1, self.model.hidden_dim)
                # Apply linear layer before
                proj_video_segments = self.vid_segment_proj(proj_video_segments)
                proj_audio_segments = self.aud_segment_proj(proj_audio_segments)
                # Calculate loss
                if self.segment_split:
                    proj_video_segs = torch.chunk(proj_video_segments, chunks=128, dim=0)
                    proj_audio_segs = torch.chunk(proj_audio_segments, chunks=128, dim=0)
                    segment_loss = 0.
                    for vseg, aseg in zip(proj_video_segs, proj_audio_segs):
                        segment_loss += self.segment_infonce(vseg, aseg)
                    segment_loss = segment_loss.mean()
                else:
                    segment_loss = self.segment_infonce(proj_video_segments, proj_audio_segments)
            elif "global" in self.contrastive_mode:
                proj_vid_feat = self.vid_global_proj(proj_visual_feat.mean(1))
                proj_aud_feat = self.aud_global_proj(latent_hat.mean(1))
                global_loss = self.global_infonce(proj_vid_feat, proj_aud_feat)

            contrastive_loss = segment_loss + global_loss
        else:
            contrastive_loss = 0.0

        consistency_loss = data_match_loss = velocity_match_loss = 0.
        if self.use_consistency:
            # Equation 6 from Consistency Flow Matching paper
            # https://arxiv.org/pdf/2407.02398v1
            data_match_loss = nn.functional.mse_loss(pred_latent, ema_pred_latent)
            velocity_match_loss = nn.functional.mse_loss(pred_flow, ema_pred_flow)

            consistency_loss = data_match_loss + \
                velocity_match_loss * self.consistency_velocity_match_alpha

        loss = main_loss + \
            direction_loss * self.direction_loss_weight + \
            anchor_loss * self.anchor_loss_weight + \
            contrastive_loss * self.contrastive_loss_weight + \
            consistency_loss * self.consistency_loss_weight

        if return_contrastive:
            return loss, LossBreakdown(
                total=loss,
                main=main_loss,
                anchor_loss=anchor_loss,
                direction_loss=direction_loss,
                contrastive_loss=contrastive_loss,
                data_match=data_match_loss,
                velocity_match=velocity_match_loss
            ), (proj_video_segments, proj_audio_segments)
        else:
            return loss, LossBreakdown(
                total=loss,
                main=main_loss,
                anchor_loss=anchor_loss,
                direction_loss=direction_loss,
                contrastive_loss=contrastive_loss,
                data_match=data_match_loss,
                velocity_match=velocity_match_loss
            )
