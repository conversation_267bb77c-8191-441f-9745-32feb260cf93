import av
import numpy as np
import torch


class VideoFramesExtractor:
    def __init__(self, fps: int = 4):
        self.fps = fps

    def __call__(self, video_path: str) -> torch.Tensor:
        frames = []
        next_frame_time = 0.0
        time_delta = 1.0 / self.fps

        with av.open(video_path) as container:
            stream = container.streams.video[0]
            real_fps = stream.guessed_rate
            stream.thread_type = 'AUTO'

            for packet in container.demux(stream):
                for frame in packet.decode():
                    frame_time = frame.time
                    frame_np = None
                    while frame_time >= next_frame_time:
                        if frame_np is None:
                            frame_np = frame.to_ndarray(format='rgb24')
                        frames.append(frame_np)
                        next_frame_time += time_delta

        output_frames = torch.from_numpy(np.stack(frames))
        return output_frames, real_fps


if __name__ == "__main__":
    video_path = "./AVSync15/videos/cap_gun_shooting/4QDaiF6qioE_000048_000058_3.5_7.0.mp4"
    extractor = VideoFramesExtractor()
    frames, real_fps = extractor(video_path)
    print(frames.shape, real_fps)
