import torch
import pandas as pd
from torch.utils.data import Dataset, DataLoader, random_split
from lightning import LightningDataModule
from .preprocessing.audio import read_audio, MelConverter
from .preprocessing.video import VideoFramesExtractor


class AVDataset(Dataset):
    def __init__(
        self,
        data_file: str,
        split: str = 'train',
        fps: int = 4,
        sampling_rate: int = 16_000,
        n_fft: int = 1024,
        num_mels: int = 80,
        hop_size: int = 256,
        win_size: int = 1024,
        fmin: float = 0,
        fmax: float = 8_000,
        norm_fn=torch.log10,
    ):
        assert split in ['train', 'test']
        data = pd.read_csv(data_file, sep='\t')
        self.data = data[data['split'] == split].reset_index(drop=True)
        self.video_extractor = VideoFramesExtractor(fps=fps)
        self.audio_converter = MelConverter(
            sampling_rate=sampling_rate,
            n_fft=n_fft,
            num_mels=num_mels,
            hop_size=hop_size,
            win_size=win_size,
            fmin=fmin,
            fmax=fmax,
            norm_fn=norm_fn,
        )

    def __len__(self):
        return len(self.data)

    def __getitem__(self, idx):
        row = self.data.loc[idx]
        video_path = row['video_path']
        audio_path = row['audio_path']
        video_frames = self.video_extractor(video_path)
        audio = read_audio(audio_path)
        text = row['text_description']
        mel_spectrogram = self.audio_converter(audio)
        return video_frames, mel_spectrogram, text


