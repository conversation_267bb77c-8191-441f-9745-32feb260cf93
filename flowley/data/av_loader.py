import torch
import pandas as pd
from torch.utils.data import Dataset, DataLoader, random_split
from lightning import LightningDataModule
from .preprocessing.audio import read_audio, MelConverter
from .preprocessing.video import VideoFramesExtractor


class AVDataset(Dataset):
    def __init__(
        self,
        data_file: str,
        split: str = 'train',
        fps: int = 4,
        sampling_rate: int = 16_000,
        n_fft: int = 1024,
        num_mels: int = 80,
        hop_size: int = 256,
        win_size: int = 1024,
        fmin: float = 0,
        fmax: float = 8_000,
        norm_fn=torch.log10,
    ):
        assert split in ['train', 'test']
        data = pd.read_csv(data_file, sep='\t')
        self.data = data[data['split'] == split].reset_index(drop=True)
        self.video_extractor = VideoFramesExtractor(fps=fps)
        self.audio_converter = MelConverter(
            sampling_rate=sampling_rate,
            n_fft=n_fft,
            num_mels=num_mels,
            hop_size=hop_size,
            win_size=win_size,
            fmin=fmin,
            fmax=fmax,
            norm_fn=norm_fn,
        )

    def __len__(self):
        return len(self.data)

    def __getitem__(self, idx):
        row = self.data.loc[idx]
        video_path = row['video_path']
        audio_path = row['audio_path']
        video_frames = self.video_extractor(video_path)
        audio = read_audio(audio_path)
        text = row['text_description']
        mel_spectrogram = self.audio_converter(audio)
        return video_frames, mel_spectrogram, text


class AVDataModule(LightningDataModule):
    def __init__(
        self,
        data_file: str,
        batch_size: int = 32,
        fps: int = 4,
        sampling_rate: int = 16_000,
        n_fft: int = 1024,
        num_mels: int = 80,
        hop_size: int = 256,
        win_size: int = 1024,
        fmin: float = 0,
        fmax: float = 8_000,
        norm_fn=torch.log10,
        train_val_split: float = 0.9,
        seed: int = 42,
        num_workers: int = 4,
        pin_memory: bool = True,
    ):
        super(AVDataModule, self).__init__()
        self.save_hyperparameters()

        self.data_train = None
        self.data_val = None
        self.data_test = None

        self.batch_size_per_device = batch_size

    def setup(self, stage=None):
        params = {
            'data_file': self.hparams.data_file,
            'fps': self.hparams.fps,
            'sampling_rate': self.hparams.sampling_rate,
            'n_fft': self.hparams.n_fft,
            'num_mels': self.hparams.num_mels,
            'hop_size': self.hparams.hop_size,
            'win_size': self.hparams.win_size,
            'fmin': self.hparams.fmin,
            'fmax': self.hparams.fmax,
            'norm_fn': self.hparams.norm_fn,
        }
        if stage in ['fit', 'validate'] or stage is None:
            dataset = AVDataset(split='train', **params)
            self.data_train, self.data_val = random_split(
                dataaset=dataset,
                lengths=[
                    int(len(dataset) * self.hparams.train_val_split),
                    len(dataset) - int(len(dataset) * self.hparams.train_val_split),
                ],
                generator=torch.Generator().manual_seed(self.hparams.seed),
            )
        elif stage == 'test':
            self.test_dataset = AVDataset(split='test', **params)

    def train_dataloader(self):
        return DataLoader(
            dataset=self.data_train,
            batch_size=self.batch_size_per_device,
            num_workers=self.hparams.num_workers,
            pin_memory=self.hparams.pin_memory,
            shuffle=True,
        )

    def val_dataloader(self):
        return DataLoader(
            dataset=self.data_val,
            batch_size=self.batch_size_per_device,
            num_workers=self.hparams.num_workers,
            pin_memory=self.hparams.pin_memory,
            shuffle=False,
        )

    def test_dataloader(self):
        return DataLoader(
            dataset=self.test_dataset,
            batch_size=self.batch_size_per_device,
            num_workers=self.hparams.num_workers,
            pin_memory=self.hparams.pin_memory,
            shuffle=False,
        )
