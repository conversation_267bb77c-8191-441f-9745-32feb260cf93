import os
import pandas as pd
from tensordict import TensorDict


def load_tsv_and_memmap(root_dir, name):
    tsv_path = os.path.join(root_dir, f"{name}.tsv")
    df = pd.read_csv(tsv_path, sep='\t')
    memmap_dir = os.path.join(root_dir, name)
    td = TensorDict.load_memmap(memmap_dir)
    return df, td


if __name__ == "__main__":
    main_dir = "/cm/shared/thanhtvt/Flowley/dataset/VGGSound/memmap_full_fps8-size384/"
    ref_dir = '/cm/shared/thanhtvt/Flowley/dataset/VGGSound/memmap_full/'

    print("Loading memmap and tsv...")
    main_df, main_td = load_tsv_and_memmap(main_dir, "test")
    ref_df, ref_td = load_tsv_and_memmap(ref_dir, "test")

    print("Get features")
    video_features = main_td["video_features"]
    text_features = ref_td["text_features"]
    text_len = ref_td["text_len"]
    mean = ref_td["mean"]
    std = ref_td["std"]

    print("Re-ordering...")
    ref_df["ids"] = range(len(ref_df))
    output_df = main_df.merge(
        ref_df.drop_duplicates(subset=["path"]),
        on=['path', 'caption'],
        how='left'
    )
    ref_ids = output_df.ids.tolist()

    text_features = text_features[ref_ids]
    text_len = text_len[ref_ids]
    mean = mean[ref_ids]
    std = std[ref_ids]

    print("Saving dataframe")
    output_df = output_df.drop(columns=["ids"])
    output_df.to_csv(os.path.join(ref_dir, "test_.tsv"), sep="\t", index=False)

    print("Proceed to save TensorDict")
    output_data = {
        "mean": mean,
        "std": std,
        "video_features": video_features,
        "text_features": text_features,
        "text_len": text_len,
    }
    TensorDict(
        output_data,
        batch_size=text_len.size(0)
    ).memmap_(os.path.join(ref_dir, "test_"), copy_existing=True)

    print("Finish saving TensorDict.")
