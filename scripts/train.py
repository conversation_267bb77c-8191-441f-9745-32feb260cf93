import hydra
import rootutils
from omegaconf import DictConfig
from accelerate.logging import get_logger
from flowley.runner import Runner

logger = get_logger(__name__)

rootutils.setup_root(__file__, indicator=".project_root", pythonpath=True)


@hydra.main(version_base="1.3.2", config_path="../configs", config_name="train.yaml")
def train(cfg: DictConfig):
    runner = Runner(cfg)
    runner.train()


if __name__ == "__main__":
    train()
