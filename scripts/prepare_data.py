import os
import pandas as pd
import argparse
import multiprocessing as mp
from functools import partial
from glob import glob
from tqdm import tqdm


def parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument("--data_root", type=str, default="./data", help="Root data directory.")
    parser.add_argument(
        "--dataset",
        choices=["VGGSound", "Landscape", "Greatest_Hits", "CountixAV", "AVSync15"],
        required=True,
        help="Dataset to prepare."
    )
    args = parser.parse_args()
    return args


def move_to_split(df, data_path, audiofile):
    audio_name = os.path.basename(audiofile)
    if audio_name in df["name"].to_list():
        split = df[df["name"] == audio_name]["split"].item()
        savedir = os.path.join(data_path, split)
        os.makedirs(savedir, exist_ok=True)
        # shutil.move(audiofile, os.path.join(savedir, audio_name))
        os.symlink(audiofile, os.path.join(savedir, audio_name))


def prepare_vggsound(args):
    data_path = os.path.join(args.data_root, args.dataset)
    csv_file = os.path.join(data_path, "vggsound.csv")
    df = pd.read_csv(csv_file, header=None, names=["id", "start", "label", "split"])
    df["name"] = df.apply(lambda x: f"{x['id']}_{str(x['start']).zfill(6)}.mp4", axis=1)
    list_files = glob(os.path.join(data_path, "**/*.mp4"), recursive=True)

    # def move_to_split(audiofile):
    #     audio_name = os.path.basename(audiofile)
    #     split = df[df["name"] == audio_name]["split"].item()
    #     savedir = os.path.join(data_path, split)
    #     os.makedirs(savedir, exist_ok=True)
    #     print(savedir)
    #     shutil.move(audiofile, os.path.join(savedir, audio_name))

    with mp.Pool(processes=mp.cpu_count() // 2) as pool:
        # pool.map(move_to_split, list_files)
        _ = list(tqdm(pool.imap_unordered(partial(move_to_split, df, data_path), list_files),
                      total=len(list_files)))

    # process_map(move_to_split, list_files, max_workers=mp.cpu_count() // 2)


def main(args):
    if args.dataset == "VGGSound":
        prepare_vggsound(args)
    else:
        raise NotImplementedError


if __name__ == "__main__":
    args = parse_args()
    main(args)
