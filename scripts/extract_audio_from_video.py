import argparse
import multiprocessing as mp
import os
import glob


def parse_args():
    parser = argparse.ArgumentParser(description='Extract audio from video files')
    parser.add_argument('input_dir', type=str, help='Input directory containing video files')
    parser.add_argument('--num_workers',
                        type=int,
                        default=mp.cpu_count() // 2,
                        help='Number of workers to use')
    parser.add_argument('--ffmpeg_path', type=str, default="ffmpeg", help='Path to ffmpeg.')
    return parser.parse_args()


def extract_audio(input_file, ffmpeg_path):
    output_file = input_file.replace("videos", "audios").replace(".mp4", ".wav")
    os.makedirs(os.path.dirname(output_file), exist_ok=True)
    os.system(f'{ffmpeg_path} -i {input_file} -vn -acodec pcm_s16le -ar 16000 -ac 1 {output_file}')


def main():
    args = parse_args()
    input_files = glob.glob(os.path.join(args.input_dir, '**/*.mp4'), recursive=True)

    with mp.Pool(args.num_workers) as pool:
        pool.starmap(extract_audio, [(input_file, args.ffmpeg_path)
                                     for input_file in input_files])


if __name__ == '__main__':
    main()
