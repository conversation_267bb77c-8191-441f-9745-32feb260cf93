<div align="center">

<h1>Flowley: Synchronized Video-to-Audio Synthesis with Rectified Flow Matching</h1>

</div>

## Table of Contents
- [Installation](#installation)
- [Training](#training)
- [Inference](#inference)
- [Acknowledgement](#acknowledgement)

## Installation

We have only tested this on Ubuntu and PyTorch 2.0+

#### 1. Clone the repository:
```shell
git clone https://<EMAIL>/Video2Audio/Flowley/_git/Flowley
```
Enter passkey if it is required.

#### 2. Install dependencies
We use PyTorch 2.2 with CUDA 11.8. Please check if your GPUs/driver support this.
```shell
cd Flowley
conda env create -f environment.yml
```

#### 3. Install project
```shell
pip install -e .
```

## Ideas/To-Do List
[x] A fully-functional framework that can handle training and inference.  
[x] Velocity direction loss in rectfied flow matching [[paper](https://arxiv.org/pdf/2410.10356)]  
[x] Soft-masking with local attention for audio-video synchronization in SingleStreamBlock [[paper](https://arxiv.org/pdf/2206.00182)] [[FlexAttn](https://pytorch.org/blog/flexattention/#sliding-window--causal)]  
[ ] Follow the training of SyncNet, perform **contrastive learning** between frames of audio and frames of video for synchronization [[SyncNet](https://www.robots.ox.ac.uk/~vgg/publications/2016/Chung16a/chung16a.pdf)]  
[ ] REPresentation Alignment [[RePA](https://openreview.net/forum?id=DJSZGGZYVi)] [[HTS-AT](https://ieeexplore.ieee.org/abstract/document/9746312)]
[ ] Re-train new VAE with better latent representation [[ref1](https://arxiv.org/pdf/2502.00359)] [[ref2](https://arxiv.org/pdf/2501.01423)] [[ref3](https://arxiv.org/pdf/2209.15352)]  
[ ] Negative prompting to exclude unwanted audio elements [[paper](https://arxiv.org/pdf/2411.17698)]  
[ ] Incorporate music captioning model (if any) to enhance ground-truth caption's expressiveness  [[paper](https://arxiv.org/pdf/2410.13720)]  
[ ] Try with different text/visual encoder (e.g., [LanguageBind](https://arxiv.org/pdf/2310.01852), [CLAP](https://arxiv.org/pdf/2211.06687)) as it has been proven to improve scores [[paper1](https://arxiv.org/pdf/2412.03603)] [[paper2](https://arxiv.org/pdf/2409.10819)]

## Training
See [TRAINING.md](./docs/TRAINING.md)

## Inference
See [INFERENCE.md](./docs/INFERENCE.md)

## Acknowledgement
We would like to thank the authors for their great work.
- [Make-An-Audio-2](https://github.com/bytedance/Make-An-Audio-2)
- [MMAudio](https://github.com/hkchengrex/MMAudio)
- [rectified-flow-pytorch](https://github.com/lucidrains/rectified-flow-pytorch)