import pytest
import torch
from flowley.model.modules import (
    TextVisualAligner,
    SingleModalStreamBlockDiT,
    SingleModalStreamBlockFLUX,
    SingleModalStreamBlockStepDiT,
    MultiModalStreamBlock,
    FinalBlock,
)
from flowley.model.modules.text_visual_aligner import TextualSpatialAwareModule
from flowley.model import Flowley
from flowley.model.flow_matching import RectifiedFlowMatching


@pytest.fixture
def params():
    num_heads = 4
    return {
        "num_heads": num_heads,
        "audio_dim": 20,
        "text_dim": 768,
        "video_dim": 1024,
        "hidden_dim": 64 * num_heads,
        "audio_seq_len": 250,
        "visual_seq_len": 32,
        "text_seq_len": 77,
        "visual_num_patches": 257,
        "latent_mean": torch.zeros(20),
        "latent_std": torch.ones(20),
        "ff_multiplier": 4,
        "num_grid": 16,
    }


@pytest.fixture
def batch_size():
    return 2


@pytest.fixture
def device():
    return torch.device("cuda")


def test_textual_spatial_aware_module_output_shape(params, batch_size, device):
    textual_spatial_aware_module = TextualSpatialAwareModule(
        dim=params["hidden_dim"],
        ff_multiplier=params["ff_multiplier"],
    ).to(device)

    text_feat = torch.randn(batch_size,
                            params["text_seq_len"],
                            params["hidden_dim"],
                            device=device)
    video_feat = torch.randn(batch_size,
                             params["visual_seq_len"],
                             params["num_grid"]**2 + 1,
                             params["hidden_dim"],
                             device=device)

    with torch.inference_mode():
        output = textual_spatial_aware_module(text_feat, video_feat)

    assert output.shape == (batch_size, params["visual_seq_len"], params["hidden_dim"])


def test_text_visual_aligner_output_shape(params, batch_size, device):
    text_visual_aligner = TextVisualAligner(
        hidden_dim=params["hidden_dim"],
        ff_multiplier=params["ff_multiplier"],
    ).to(device)

    text_feat = torch.randn(batch_size,
                            params["text_seq_len"],
                            params["hidden_dim"],
                            device=device)
    video_feat = torch.randn(batch_size,
                             params["visual_seq_len"],
                             params["num_grid"]**2 + 1,
                             params["hidden_dim"],
                             device=device)

    with torch.inference_mode():
        output = text_visual_aligner(text_feat, video_feat)

    assert output.shape == (batch_size, params["visual_seq_len"], params["hidden_dim"])


def test_single_modal_stream_block_output_shape(batch_size, params, device):
    single_modal_stream_block = SingleModalStreamBlockDiT(
        dim=params["hidden_dim"],
        n_heads=params["num_heads"],
        mlp_ratio=params["ff_multiplier"],
    ).to(device)

    x = torch.randn(batch_size, params["audio_seq_len"], params["hidden_dim"], device=device)
    c = torch.randn(batch_size, params["hidden_dim"], device=device)
    rot = torch.randn(batch_size, params["audio_seq_len"], params["hidden_dim"] // params["num_heads"] // 2, 2, 2, device=device)

    with torch.inference_mode():
        output = single_modal_stream_block(x, c, rot)

    assert output.shape == x.shape


def test_single_modal_stream_block_step_dit_output_shape(batch_size, params, device):
    single_modal_stream_block = SingleModalStreamBlockStepDiT(
        dim=params["hidden_dim"],
        n_heads=params["num_heads"],
        mlp_ratio=params["ff_multiplier"],
    ).to(device)

    audio_feat = torch.randn(batch_size, params["audio_seq_len"], params["hidden_dim"], device=device)
    visual_feat = torch.randn(batch_size, params["visual_seq_len"], params["hidden_dim"], device=device)
    cond = torch.randn(batch_size, params["hidden_dim"], device=device)
    rot = torch.randn(batch_size, params["audio_seq_len"], params["hidden_dim"] // params["num_heads"] // 2, 2, 2, device=device)
    mask = torch.randint(low=0, high=2, size=(batch_size, params["num_heads"], params["audio_seq_len"], params["visual_seq_len"]), device=device).to(torch.bool)

    with torch.inference_mode():
        output = single_modal_stream_block(audio_feat, visual_feat, cond, rot, mask)

    assert output.shape == audio_feat.shape


def test_multi_modal_stream_block_output_shape(batch_size, params, device):
    multi_modal_stream_block = MultiModalStreamBlock(
        dim=params["hidden_dim"],
        n_heads=params["num_heads"],
        mlp_ratio=params["ff_multiplier"],
    ).to(device)

    head_dim = params["hidden_dim"] // params["num_heads"]
    audio = torch.randn(batch_size, params["audio_seq_len"], params["hidden_dim"], device=device)
    visual = torch.randn(batch_size, params["visual_seq_len"], params["hidden_dim"], device=device)
    text = torch.randn(batch_size, params["text_seq_len"], params["hidden_dim"], device=device)
    c = torch.randn(batch_size, params["hidden_dim"], device=device)
    audio_rot = torch.randn(batch_size, params["audio_seq_len"], head_dim // 2, 2, 2, device=device)
    visual_rot = torch.randn(batch_size, params["visual_seq_len"], head_dim // 2, 2, 2, device=device)

    with torch.inference_mode():
        latent, vis_feat, text_feat = multi_modal_stream_block(audio, visual, text, c, audio_rot, visual_rot)

    assert latent.shape == audio.shape
    assert vis_feat.shape == visual.shape
    assert text_feat.shape == text.shape


def test_final_block_output_shape(params, batch_size, device):
    final_block = FinalBlock(dim=params["hidden_dim"], out_dim=params["audio_dim"]).to(device)

    latent = torch.randn(batch_size, params["audio_seq_len"], params["hidden_dim"], device=device)
    condition = torch.randn(batch_size, params["hidden_dim"], device=device)

    with torch.inference_mode():
        output = final_block(latent, condition)

    assert output.shape == (batch_size, params["audio_seq_len"], params["audio_dim"])


def test_flowley_output_shape(params, batch_size, device):
    audio_latent = torch.randn(
        batch_size, params["audio_seq_len"], params["audio_dim"], device=device
    )
    text_feature = torch.randn(
        batch_size, params["text_seq_len"], params["text_dim"], device=device
    )
    visual_feature = torch.randn(
        batch_size, params["visual_seq_len"], params["num_grid"]**2 + 1, params["video_dim"], device=device
    )
    t = torch.rand(batch_size, device=device)

    latent_mean = audio_latent.mean(dim=(0, 1))
    latent_std = audio_latent.std(dim=(0, 1))

    model = Flowley(
        audio_dim=params["audio_dim"],
        text_dim=params["text_dim"],
        video_dim=params["video_dim"],
        hidden_dim=params["hidden_dim"],
        audio_seq_len=params["audio_seq_len"],
        visual_seq_len=params["visual_seq_len"],
        text_seq_len=params["text_seq_len"],
        visual_num_patches=params["visual_num_patches"],
        single_type="Step",
        latent_mean=latent_mean,
        latent_std=latent_std,
    ).to(device)

    with torch.inference_mode():
        flow = model(audio_latent, text_feature, visual_feature, t)

    assert flow.shape == audio_latent.shape


def test_rfm(params, batch_size, device):
    audio_latent = torch.randn(
        batch_size, params["audio_seq_len"], params["audio_dim"], device=device
    )
    text_feature = torch.randn(
        batch_size, params["text_seq_len"], params["text_dim"], device=device
    )
    visual_feature = torch.randn(
        batch_size, params["visual_seq_len"], params["num_grid"]**2 + 1, params["video_dim"], device=device
    )
    t = torch.rand(batch_size, device=device)

    latent_mean = audio_latent.mean(dim=(0, 1))
    latent_std = audio_latent.std(dim=(0, 1))

    model = Flowley(
        audio_dim=params["audio_dim"],
        text_dim=params["text_dim"],
        video_dim=params["video_dim"],
        hidden_dim=params["hidden_dim"],
        audio_seq_len=params["audio_seq_len"],
        visual_seq_len=params["visual_seq_len"],
        text_seq_len=params["text_seq_len"],
        visual_num_patches=params["visual_num_patches"],
        single_type="Step",
        latent_mean=latent_mean,
        latent_std=latent_std,
    ).to(device)

    rfm = RectifiedFlowMatching(model).to(device)

    loss, loss_breakdown = rfm(audio_latent, visual_feature, text_feature)
